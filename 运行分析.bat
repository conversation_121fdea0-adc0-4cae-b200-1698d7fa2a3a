@echo off
chcp 65001 >nul
echo ===================================
echo     敏感内容分析工具
echo ===================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查Excel文件是否存在
if not exist "5.xlsx" (
    echo 错误: 找不到文件 "5.xlsx"
    echo 请确保Excel文件存在于当前目录中
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
python -c "import pandas, requests, openpyxl" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 依赖检查完成
echo.

REM 运行分析脚本
python run_analysis.py

echo.
echo 按任意键退出...
pause >nul
