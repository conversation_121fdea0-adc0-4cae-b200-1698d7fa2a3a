@echo off
chcp 65001 >nul
echo ===================================
echo     敏感内容分析工具
echo ===================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查是否有Excel文件
set "excel_found="
for %%f in (*.xlsx) do set "excel_found=1"
if not defined excel_found (
    echo 警告: 当前目录中未找到Excel文件
    echo 请确保要分析的Excel文件存在于当前目录中
    echo.
)

REM 检查依赖是否安装
echo 检查依赖包...
python -c "import pandas, requests, openpyxl" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 依赖检查完成
echo.

REM 运行分析脚本
python run_analysis.py

echo.
echo 按任意键退出...
pause >nul
