#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感内容分析工具演示脚本
展示主要功能和结果
"""

import pandas as pd
import os

def show_analysis_results():
    """展示分析结果"""
    print("=" * 60)
    print("           敏感内容分析工具 - 结果展示")
    print("=" * 60)
    
    # 检查结果文件
    result_file = "敏感内容分析结果.xlsx"
    if not os.path.exists(result_file):
        print("❌ 未找到分析结果文件，请先运行分析")
        return
    
    try:
        # 读取Excel文件的所有工作表
        xl = pd.ExcelFile(result_file)
        print(f"📊 结果文件包含 {len(xl.sheet_names)} 个工作表:")
        for i, sheet in enumerate(xl.sheet_names, 1):
            print(f"   {i}. {sheet}")
        
        print("\n" + "=" * 60)
        
        # 显示涉政言论
        if '涉政言论' in xl.sheet_names:
            political_df = pd.read_excel(result_file, sheet_name='涉政言论')
            print(f"🏛️  涉政言论统计:")
            print(f"   总数量: {len(political_df)} 条")
            
            if len(political_df) > 0:
                # 按敏感程度统计
                severity_counts = political_df['敏感程度'].value_counts().sort_index()
                print(f"   敏感程度分布:")
                for severity, count in severity_counts.items():
                    print(f"     等级{severity}: {count}条")
                
                print(f"\n   示例记录:")
                for i, row in political_df.head(3).iterrows():
                    print(f"     ID: {row['id']} | 敏感度: {row['敏感程度']} | 内容: {str(row['敏感言论'])[:50]}...")
        
        print("\n" + "-" * 60)
        
        # 显示其他敏感内容
        if '其他敏感内容' in xl.sheet_names:
            other_df = pd.read_excel(result_file, sheet_name='其他敏感内容')
            print(f"⚠️  其他敏感内容统计:")
            print(f"   总数量: {len(other_df)} 条")
            
            if len(other_df) > 0:
                # 按类型统计
                type_counts = other_df['敏感类型'].value_counts()
                print(f"   类型分布:")
                for type_name, count in type_counts.items():
                    print(f"     {type_name}: {count}条")
        
        print("\n" + "-" * 60)
        
        # 显示各分类详情
        sensitive_categories = ['色情内容', '暴力内容', '歧视言论', '谣言传播', '诈骗信息', '仇恨言论', '违法内容', '其他敏感']
        category_found = False
        
        for category in sensitive_categories:
            if category in xl.sheet_names:
                category_df = pd.read_excel(result_file, sheet_name=category)
                if len(category_df) > 0:
                    if not category_found:
                        print(f"📋 详细分类统计:")
                        category_found = True
                    print(f"   {category}: {len(category_df)}条")
        
        # 显示失败记录
        if '分析失败记录' in xl.sheet_names:
            failed_df = pd.read_excel(result_file, sheet_name='分析失败记录')
            print(f"\n❌ 分析失败记录: {len(failed_df)} 条")
            if len(failed_df) > 0:
                print("   建议运行 retry_failed.py 重试失败记录")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"❌ 读取结果文件时发生错误: {e}")

def show_log_summary():
    """显示日志摘要"""
    print("📝 日志文件摘要:")
    
    # 分析日志
    if os.path.exists("analysis.log"):
        with open("analysis.log", "r", encoding="utf-8") as f:
            lines = f.readlines()
        print(f"   analysis.log: {len(lines)} 行日志")
        
        # 统计关键信息
        info_count = sum(1 for line in lines if " - INFO - " in line)
        warning_count = sum(1 for line in lines if " - WARNING - " in line)
        error_count = sum(1 for line in lines if " - ERROR - " in line)
        
        print(f"     信息: {info_count} 条")
        print(f"     警告: {warning_count} 条") 
        print(f"     错误: {error_count} 条")
    
    # 错误日志
    if os.path.exists("error.log"):
        with open("error.log", "r", encoding="utf-8") as f:
            error_lines = f.readlines()
        if error_lines:
            print(f"   error.log: {len(error_lines)} 行错误日志")
        else:
            print(f"   error.log: 无错误记录")
    
    # 失败记录CSV
    failed_csv = "敏感内容分析结果_失败记录.csv"
    if os.path.exists(failed_csv):
        failed_df = pd.read_csv(failed_csv, encoding='utf-8-sig')
        print(f"   失败记录: {len(failed_df)} 条")
    else:
        print(f"   失败记录: 无")

def show_file_info():
    """显示文件信息"""
    print("📁 生成的文件:")
    
    files_info = [
        ("敏感内容分析结果.xlsx", "主要分析结果"),
        ("analysis.log", "完整分析日志"),
        ("error.log", "错误日志"),
        ("敏感内容分析结果_失败记录.csv", "失败记录（如有）"),
    ]
    
    for filename, description in files_info:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            size_str = f"{size:,} 字节" if size < 1024 else f"{size/1024:.1f} KB"
            print(f"   ✅ {filename} ({size_str}) - {description}")
        else:
            print(f"   ❌ {filename} - {description}")

def main():
    """主函数"""
    show_analysis_results()
    print()
    show_log_summary()
    print()
    show_file_info()
    
    print("\n" + "=" * 60)
    print("💡 提示:")
    print("   - 如有失败记录，运行: python retry_failed.py")
    print("   - 重新分析，运行: python run_analysis.py")
    print("   - 测试API，运行: python test_api.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
