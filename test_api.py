#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API连接和敏感内容分析功能
"""

import pandas as pd
import requests
import json
import logging
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_connection():
    """测试API连接"""
    api_url = "https://one-api.zaoniao.vip"
    api_key = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
    model = "gpt-5-nano-2025-08-07"
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    test_content = "今天天气很好，适合出去散步。"
    
    prompt = f"""
请分析以下文本内容是否包含敏感信息，并进行分类：

文本内容："{test_content}"

请按照以下格式回复（只回复JSON格式，不要其他内容）：
{{
    "is_sensitive": true/false,
    "category": "涉政言论"/"其他"/"无敏感内容",
    "reason": "简要说明判断理由"
}}

敏感信息分类标准：
1. 涉政言论：包含政治敏感词汇、政治立场表达、政府批评、政治人物评论等
2. 其他：包含色情、暴力、歧视、谣言、诈骗等其他敏感内容
3. 无敏感内容：正常的日常交流内容

请严格按照JSON格式回复。
"""

    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    try:
        print("正在测试API连接...")
        response = requests.post(
            f"{api_url}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30,
            verify=False  # 忽略SSL证书验证
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content_result = result['choices'][0]['message']['content'].strip()
            print(f"API响应内容: {content_result}")
            
            # 尝试解析JSON
            try:
                analysis = json.loads(content_result)
                print("JSON解析成功:")
                print(f"  是否敏感: {analysis.get('is_sensitive')}")
                print(f"  分类: {analysis.get('category')}")
                print(f"  理由: {analysis.get('reason')}")
                return True
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                return False
        else:
            print(f"API请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"测试API时发生错误: {e}")
        return False

def test_excel_reading():
    """测试Excel文件读取"""
    try:
        print("\n正在测试Excel文件读取...")
        df = pd.read_excel('5.xlsx', sheet_name='Sheet1', header=None)
        
        # 重命名列
        column_names = ['A', 'B', 'C', 'D', 'E'][:len(df.columns)]
        df.columns = column_names
        
        print(f"Excel文件读取成功:")
        print(f"  总行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        print("\n前3行数据:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"  行{i+1}:")
            print(f"    A列(ID): {row['A']}")
            print(f"    B列(群组): {row['B']}")
            print(f"    C列(时间): {row['C']}")
            print(f"    D列(发言): {str(row['D'])[:100]}...")
            print()
        
        return True
        
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")
        return False

def test_sample_analysis():
    """测试对样本数据的分析"""
    try:
        print("\n正在测试样本数据分析...")
        
        # 读取Excel文件
        df = pd.read_excel('5.xlsx', sheet_name='Sheet1', header=None)
        column_names = ['A', 'B', 'C', 'D', 'E'][:len(df.columns)]
        df.columns = column_names
        
        # 分析前3行有内容的数据
        api_url = "https://one-api.zaoniao.vip"
        api_key = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
        model = "gpt-5-nano-2025-08-07"
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        analyzed_count = 0
        for index, row in df.iterrows():
            if analyzed_count >= 3:
                break
                
            content = str(row['D']) if pd.notna(row['D']) else ""
            if not content or content.strip() == "" or content == "nan":
                continue
                
            print(f"\n分析第{index+1}行内容:")
            print(f"内容: {content[:200]}...")
            
            prompt = f"""
请分析以下文本内容是否包含敏感信息，并进行详细分类：

文本内容："{content}"

请按照以下格式回复（只回复JSON格式，不要其他内容）：
{{
    "is_sensitive": true/false,
    "category": "涉政言论"/"色情内容"/"暴力内容"/"歧视言论"/"谣言传播"/"诈骗信息"/"仇恨言论"/"违法内容"/"其他敏感"/"无敏感内容",
    "severity": 1-10,
    "reason": "简要说明判断理由"
}}

敏感信息分类标准：
1. 涉政言论：政治敏感词汇、政治立场表达、政府批评、政治人物评论、政党讨论等
2. 色情内容：色情、性暗示、低俗内容等
3. 暴力内容：暴力描述、血腥内容、威胁恐吓等
4. 歧视言论：种族歧视、性别歧视、地域歧视等
5. 谣言传播：虚假信息、未经证实的传言等
6. 诈骗信息：诈骗、欺诈、虚假广告等
7. 仇恨言论：煽动仇恨、极端言论等
8. 违法内容：违法犯罪相关内容
9. 其他敏感：其他类型敏感内容
10. 无敏感内容：正常的日常交流内容

severity字段表示敏感程度（1-10，10最敏感）

请严格按照JSON格式回复。
"""

            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 200
            }
            
            try:
                response = requests.post(
                    f"{api_url}/v1/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=30,
                    verify=False  # 忽略SSL证书验证
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content_result = result['choices'][0]['message']['content'].strip()
                    
                    try:
                        analysis = json.loads(content_result)
                        print(f"分析结果:")
                        print(f"  是否敏感: {analysis.get('is_sensitive')}")
                        print(f"  分类: {analysis.get('category')}")
                        print(f"  敏感程度: {analysis.get('severity')}")
                        print(f"  理由: {analysis.get('reason')}")
                    except json.JSONDecodeError:
                        print(f"JSON解析失败，原始响应: {content_result}")
                else:
                    print(f"API请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"分析时发生错误: {e}")
            
            analyzed_count += 1
        
        return True
        
    except Exception as e:
        print(f"样本分析时发生错误: {e}")
        return False

if __name__ == "__main__":
    print("=== 敏感内容分析工具测试 ===")
    
    # 测试API连接
    if test_api_connection():
        print("✓ API连接测试通过")
    else:
        print("✗ API连接测试失败")
        exit(1)
    
    # 测试Excel读取
    if test_excel_reading():
        print("✓ Excel文件读取测试通过")
    else:
        print("✗ Excel文件读取测试失败")
        exit(1)
    
    # 测试样本分析
    if test_sample_analysis():
        print("✓ 样本分析测试通过")
    else:
        print("✗ 样本分析测试失败")
    
    print("\n测试完成！如果所有测试都通过，可以运行完整的分析脚本。")
