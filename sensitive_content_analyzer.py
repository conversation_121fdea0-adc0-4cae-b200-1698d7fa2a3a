#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感内容分析脚本
调用大模型API分析Excel文件中的敏感信息
"""

import pandas as pd
import requests
import json
import time
from typing import Dict, List, Tuple
import logging
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SensitiveContentAnalyzer:
    def __init__(self, api_url: str, api_key: str, model: str):
        """
        初始化敏感内容分析器
        
        Args:
            api_url: API地址
            api_key: API密钥
            model: 模型名称
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model = model
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
    def analyze_content(self, content: str) -> Dict[str, str]:
        """
        分析内容是否包含敏感信息
        
        Args:
            content: 要分析的内容
            
        Returns:
            包含分析结果的字典
        """
        prompt = f"""
请分析以下文本内容是否包含敏感信息，并进行分类：

文本内容："{content}"

请按照以下格式回复（只回复JSON格式，不要其他内容）：
{{
    "is_sensitive": true/false,
    "category": "涉政言论"/"其他"/"无敏感内容",
    "reason": "简要说明判断理由"
}}

敏感信息分类标准：
1. 涉政言论：包含政治敏感词汇、政治立场表达、政府批评、政治人物评论等
2. 其他：包含色情、暴力、歧视、谣言、诈骗等其他敏感内容
3. 无敏感内容：正常的日常交流内容

请严格按照JSON格式回复。
"""

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30,
                verify=False  # 忽略SSL证书验证
            )
            
            if response.status_code == 200:
                result = response.json()
                content_result = result['choices'][0]['message']['content'].strip()
                
                # 尝试解析JSON响应
                try:
                    analysis = json.loads(content_result)
                    return analysis
                except json.JSONDecodeError:
                    logger.warning(f"无法解析API响应为JSON: {content_result}")
                    return {
                        "is_sensitive": False,
                        "category": "无敏感内容",
                        "reason": "API响应格式错误"
                    }
            else:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return {
                    "is_sensitive": False,
                    "category": "无敏感内容",
                    "reason": "API请求失败"
                }
                
        except Exception as e:
            logger.error(f"分析内容时发生错误: {str(e)}")
            return {
                "is_sensitive": False,
                "category": "无敏感内容",
                "reason": f"分析错误: {str(e)}"
            }
    
    def process_excel_file(self, file_path: str, max_rows: int = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        处理Excel文件，分析敏感内容

        Args:
            file_path: Excel文件路径
            max_rows: 最大分析行数，None表示分析全部

        Returns:
            涉政言论DataFrame和其他敏感内容DataFrame的元组
        """
        try:
            # 读取Excel文件，不使用第一行作为列名
            df = pd.read_excel(file_path, sheet_name='Sheet1', header=None)
            logger.info(f"成功读取Excel文件，共{len(df)}行数据")

            # 重命名列为A、B、C、D等
            column_names = ['A', 'B', 'C', 'D', 'E'][:len(df.columns)]
            df.columns = column_names

            logger.info(f"Excel文件列结构: {list(df.columns)}")
            logger.info(f"前3行数据预览:")
            logger.info(df.head(3).to_string())

            # 初始化结果列表
            political_sensitive = []  # 涉政言论
            other_sensitive = []      # 其他敏感内容

            # 限制分析行数
            total_rows = len(df)
            if max_rows:
                df = df.head(max_rows)
                logger.info(f"限制分析行数: {max_rows}/{total_rows}")

            # 遍历每一行进行分析
            for index, row in df.iterrows():
                # D列是发言内容（第4列，索引为3）
                content = str(row['D']) if pd.notna(row['D']) else ""

                if not content or content.strip() == "" or content == "nan":
                    continue

                logger.info(f"正在分析第{index+1}行内容: {content[:100]}...")

                # 调用API分析内容
                analysis = self.analyze_content(content)

                # 如果检测到敏感内容
                if analysis.get('is_sensitive', False):
                    sensitive_data = {
                        'id': row['A'],
                        '敏感言论': row['D'],
                        '时间': row['C'],
                        '群组': row['B']
                    }

                    if analysis.get('category') == '涉政言论':
                        political_sensitive.append(sensitive_data)
                        logger.info(f"检测到涉政言论: {content[:50]}...")
                    elif analysis.get('category') == '其他':
                        other_sensitive.append(sensitive_data)
                        logger.info(f"检测到其他敏感内容: {content[:50]}...")

                # 添加延迟避免API限制
                time.sleep(0.5)

            # 创建DataFrame
            political_df = pd.DataFrame(political_sensitive)
            other_df = pd.DataFrame(other_sensitive)

            logger.info(f"分析完成！涉政言论: {len(political_df)}条，其他敏感内容: {len(other_df)}条")

            return political_df, other_df

        except Exception as e:
            logger.error(f"处理Excel文件时发生错误: {str(e)}")
            raise
    
    def save_results(self, political_df: pd.DataFrame, other_df: pd.DataFrame, output_file: str):
        """
        保存分析结果到Excel文件
        
        Args:
            political_df: 涉政言论DataFrame
            other_df: 其他敏感内容DataFrame
            output_file: 输出文件路径
        """
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                political_df.to_excel(writer, sheet_name='涉政言论', index=False)
                other_df.to_excel(writer, sheet_name='其他敏感内容', index=False)
            
            logger.info(f"结果已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"保存结果时发生错误: {str(e)}")
            raise

def main():
    """主函数"""
    # API配置
    API_URL = "https://one-api.zaoniao.vip"
    API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
    MODEL = "gpt-5-nano-2025-08-07"

    # 文件路径
    INPUT_FILE = "5.xlsx"
    OUTPUT_FILE = "敏感内容分析结果.xlsx"

    print("=== 敏感内容分析工具 ===")
    print(f"输入文件: {INPUT_FILE}")
    print(f"输出文件: {OUTPUT_FILE}")
    print(f"使用模型: {MODEL}")

    # 询问用户是否要限制分析行数（用于测试）
    try:
        limit_input = input("\n是否限制分析行数？(输入数字限制行数，直接回车分析全部): ").strip()
        max_rows = int(limit_input) if limit_input else None
        if max_rows:
            print(f"将只分析前 {max_rows} 行数据")
    except ValueError:
        max_rows = None
        print("输入无效，将分析全部数据")

    try:
        # 创建分析器实例
        analyzer = SensitiveContentAnalyzer(API_URL, API_KEY, MODEL)

        # 处理Excel文件
        logger.info("开始分析Excel文件...")
        political_df, other_df = analyzer.process_excel_file(INPUT_FILE, max_rows)

        # 保存结果
        analyzer.save_results(political_df, other_df, OUTPUT_FILE)

        # 打印统计信息
        print(f"\n=== 分析完成 ===")
        print(f"涉政言论数量: {len(political_df)}")
        print(f"其他敏感内容数量: {len(other_df)}")
        print(f"结果已保存到: {OUTPUT_FILE}")

        if len(political_df) > 0:
            print(f"\n涉政言论示例:")
            print(political_df.head().to_string())

        if len(other_df) > 0:
            print(f"\n其他敏感内容示例:")
            print(other_df.head().to_string())

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()
