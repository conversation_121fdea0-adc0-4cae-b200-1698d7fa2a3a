#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感内容分析脚本
调用大模型API分析Excel文件中的敏感信息
"""

import pandas as pd
import requests
import json
import time
from typing import Dict, Tuple
import logging
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SensitiveContentAnalyzer:
    def __init__(self, api_url: str, api_key: str, model: str):
        """
        初始化敏感内容分析器
        
        Args:
            api_url: API地址
            api_key: API密钥
            model: 模型名称
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model = model
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
    def analyze_content(self, content: str) -> Dict[str, str]:
        """
        分析内容是否包含敏感信息
        
        Args:
            content: 要分析的内容
            
        Returns:
            包含分析结果的字典
        """
        prompt = f"""
请分析以下文本内容是否包含敏感信息，并进行详细分类：

文本内容："{content}"

请按照以下格式回复（只回复JSON格式，不要其他内容）：
{{
    "is_sensitive": true/false,
    "category": "涉政言论"/"色情内容"/"暴力内容"/"歧视言论"/"谣言传播"/"诈骗信息"/"仇恨言论"/"违法内容"/"其他敏感"/"无敏感内容",
    "severity": 1-10,
    "reason": "简要说明判断理由"
}}

敏感信息分类标准：
1. 涉政言论：政治敏感词汇、政治立场表达、政府批评、政治人物评论、政党讨论等
2. 色情内容：色情、性暗示、低俗内容等
3. 暴力内容：暴力描述、血腥内容、威胁恐吓等
4. 歧视言论：种族歧视、性别歧视、地域歧视等
5. 谣言传播：虚假信息、未经证实的传言等
6. 诈骗信息：诈骗、欺诈、虚假广告等
7. 仇恨言论：煽动仇恨、极端言论等
8. 违法内容：违法犯罪相关内容
9. 其他敏感：其他类型敏感内容
10. 无敏感内容：正常的日常交流内容

severity字段表示敏感程度（1-10，10最敏感）

请严格按照JSON格式回复。
"""

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30,
                verify=False  # 忽略SSL证书验证
            )
            
            if response.status_code == 200:
                result = response.json()
                content_result = result['choices'][0]['message']['content'].strip()
                
                # 尝试解析JSON响应
                try:
                    analysis = json.loads(content_result)
                    return analysis
                except json.JSONDecodeError:
                    logger.warning(f"无法解析API响应为JSON: {content_result}")
                    return {
                        "is_sensitive": False,
                        "category": "无敏感内容",
                        "reason": "API响应格式错误"
                    }
            else:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return {
                    "is_sensitive": False,
                    "category": "无敏感内容",
                    "reason": "API请求失败"
                }
                
        except Exception as e:
            logger.error(f"分析内容时发生错误: {str(e)}")
            return {
                "is_sensitive": False,
                "category": "无敏感内容",
                "reason": f"分析错误: {str(e)}"
            }
    
    def process_excel_file(self, file_path: str, max_rows: int = None) -> Tuple[pd.DataFrame, pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        处理Excel文件，分析敏感内容

        Args:
            file_path: Excel文件路径
            max_rows: 最大分析行数，None表示分析全部

        Returns:
            涉政言论DataFrame、其他敏感内容总DataFrame和分类敏感内容字典的元组
        """
        try:
            # 读取Excel文件，不使用第一行作为列名
            df = pd.read_excel(file_path, sheet_name='Sheet1', header=None)
            logger.info(f"成功读取Excel文件，共{len(df)}行数据")

            # 重命名列为A、B、C、D等
            column_names = ['A', 'B', 'C', 'D', 'E'][:len(df.columns)]
            df.columns = column_names

            logger.info(f"Excel文件列结构: {list(df.columns)}")
            logger.info(f"前3行数据预览:")
            logger.info(df.head(3).to_string())

            # 初始化结果字典
            political_sensitive = []  # 涉政言论
            other_sensitive_all = []  # 其他敏感内容总表
            categorized_sensitive = {  # 分类敏感内容
                '色情内容': [],
                '暴力内容': [],
                '歧视言论': [],
                '谣言传播': [],
                '诈骗信息': [],
                '仇恨言论': [],
                '违法内容': [],
                '其他敏感': []
            }

            # 用于去重的字典：存储每个ID的最敏感内容
            id_max_sensitive = {}  # {id: {'data': row_data, 'severity': severity, 'category': category}}

            # 限制分析行数
            total_rows = len(df)
            if max_rows:
                df = df.head(max_rows)
                logger.info(f"限制分析行数: {max_rows}/{total_rows}")

            # 遍历每一行进行分析
            for index, row in df.iterrows():
                # D列是发言内容（第4列，索引为3）
                content = str(row['D']) if pd.notna(row['D']) else ""

                if not content or content.strip() == "" or content == "nan":
                    continue

                logger.info(f"正在分析第{index+1}行内容: {content[:100]}...")

                # 调用API分析内容
                analysis = self.analyze_content(content)

                # 如果检测到敏感内容
                if analysis.get('is_sensitive', False):
                    user_id = row['A']
                    category = analysis.get('category', '其他敏感')
                    severity = analysis.get('severity', 1)

                    sensitive_data = {
                        'id': user_id,
                        '敏感言论': row['D'],
                        '时间': row['C'],
                        '群组': row['B'],
                        '敏感类型': category,
                        '敏感程度': severity
                    }

                    # 处理涉政言论
                    if category == '涉政言论':
                        # 检查是否已有该ID的涉政言论
                        if user_id not in id_max_sensitive or id_max_sensitive[user_id]['category'] != '涉政言论':
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                        elif severity > id_max_sensitive[user_id]['severity']:
                            # 更新为更敏感的内容
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                        logger.info(f"检测到涉政言论 (敏感度{severity}): {content[:50]}...")

                    # 处理其他敏感内容
                    else:
                        # 检查是否已有该ID的记录
                        if user_id not in id_max_sensitive:
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                        elif id_max_sensitive[user_id]['category'] != '涉政言论' and severity > id_max_sensitive[user_id]['severity']:
                            # 如果不是涉政言论且当前更敏感，则更新
                            id_max_sensitive[user_id] = {
                                'data': sensitive_data,
                                'severity': severity,
                                'category': category
                            }
                        logger.info(f"检测到{category} (敏感度{severity}): {content[:50]}...")

                # 添加延迟避免API限制
                time.sleep(0.5)

            # 从去重字典中提取最终结果
            for user_id, record in id_max_sensitive.items():
                data = record['data']
                category = record['category']

                if category == '涉政言论':
                    political_sensitive.append(data)
                else:
                    other_sensitive_all.append(data)
                    # 同时添加到对应的分类中
                    if category in categorized_sensitive:
                        categorized_sensitive[category].append(data)
                    else:
                        categorized_sensitive['其他敏感'].append(data)

            # 创建DataFrame
            political_df = pd.DataFrame(political_sensitive)
            other_all_df = pd.DataFrame(other_sensitive_all)

            # 创建分类DataFrame字典
            categorized_dfs = {}
            for cat_name, cat_data in categorized_sensitive.items():
                if cat_data:  # 只创建有数据的分类
                    categorized_dfs[cat_name] = pd.DataFrame(cat_data)

            logger.info(f"分析完成！涉政言论: {len(political_df)}条，其他敏感内容: {len(other_all_df)}条")
            logger.info(f"其他敏感内容分类: {', '.join([f'{k}({len(v)})' for k, v in categorized_dfs.items()])}")

            return political_df, other_all_df, categorized_dfs

        except Exception as e:
            logger.error(f"处理Excel文件时发生错误: {str(e)}")
            raise
    
    def save_results(self, political_df: pd.DataFrame, other_all_df: pd.DataFrame, categorized_dfs: Dict[str, pd.DataFrame], output_file: str):
        """
        保存分析结果到Excel文件

        Args:
            political_df: 涉政言论DataFrame
            other_all_df: 其他敏感内容总DataFrame
            categorized_dfs: 分类敏感内容DataFrame字典
            output_file: 输出文件路径
        """
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 保存涉政言论
                political_df.to_excel(writer, sheet_name='涉政言论', index=False)

                # 保存其他敏感内容总表
                other_all_df.to_excel(writer, sheet_name='其他敏感内容', index=False)

                # 保存各分类的敏感内容
                for category, df in categorized_dfs.items():
                    # Excel工作表名称有长度限制，截断过长的名称
                    sheet_name = category[:31] if len(category) > 31 else category
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

            logger.info(f"结果已保存到: {output_file}")
            logger.info(f"包含工作表: 涉政言论, 其他敏感内容, {', '.join(categorized_dfs.keys())}")

        except Exception as e:
            logger.error(f"保存结果时发生错误: {str(e)}")
            raise

def main():
    """主函数"""
    # API配置
    API_URL = "https://one-api.zaoniao.vip"
    API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
    MODEL = "gpt-5-nano-2025-08-07"

    # 文件路径
    INPUT_FILE = "5.xlsx"
    OUTPUT_FILE = "敏感内容分析结果.xlsx"

    print("=== 敏感内容分析工具 ===")
    print(f"输入文件: {INPUT_FILE}")
    print(f"输出文件: {OUTPUT_FILE}")
    print(f"使用模型: {MODEL}")

    # 询问用户是否要限制分析行数（用于测试）
    try:
        limit_input = input("\n是否限制分析行数？(输入数字限制行数，直接回车分析全部): ").strip()
        max_rows = int(limit_input) if limit_input else None
        if max_rows:
            print(f"将只分析前 {max_rows} 行数据")
    except ValueError:
        max_rows = None
        print("输入无效，将分析全部数据")

    try:
        # 创建分析器实例
        analyzer = SensitiveContentAnalyzer(API_URL, API_KEY, MODEL)

        # 处理Excel文件
        logger.info("开始分析Excel文件...")
        political_df, other_all_df, categorized_dfs = analyzer.process_excel_file(INPUT_FILE, max_rows)

        # 保存结果
        analyzer.save_results(political_df, other_all_df, categorized_dfs, OUTPUT_FILE)

        # 打印统计信息
        print(f"\n=== 分析完成 ===")
        print(f"涉政言论数量: {len(political_df)}")
        print(f"其他敏感内容总数量: {len(other_all_df)}")

        # 打印分类统计
        if categorized_dfs:
            print(f"\n其他敏感内容分类统计:")
            for category, df in categorized_dfs.items():
                print(f"  {category}: {len(df)}条")

        print(f"\n结果已保存到: {OUTPUT_FILE}")

        if len(political_df) > 0:
            print(f"\n涉政言论示例:")
            print(political_df.head().to_string())

        if len(other_all_df) > 0:
            print(f"\n其他敏感内容示例:")
            print(other_all_df.head().to_string())

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()
