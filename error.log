2025-08-15 12:46:08,715 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 12:49:54,942 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 12:50:06,309 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 12:53:03,505 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 12:53:27,826 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:00:39,823 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:00:42,041 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:01:07,890 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:01:09,154 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:01:10,108 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:01:25,557 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:01:28,047 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:01:39,913 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:02:00,020 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:02:26,003 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:02:41,763 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:07:24,564 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:07:44,729 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:07:54,618 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:07:57,571 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:08:01,025 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:08:04,777 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:08:18,273 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:08:19,766 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:08:25,980 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:08:26,258 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:08:38,871 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:08:41,468 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:08:49,649 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:09:00,969 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:07,878 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:10,706 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:15,203 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:15,296 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:18,835 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:09:26,768 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:29,156 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:09:35,674 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:36,946 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:37,903 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:39,187 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:40,245 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:09:43,120 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:09:51,180 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:54,969 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:09:58,304 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:07,872 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:08,198 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:10:12,674 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:10:17,068 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:17,068 - ERROR - 第108行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:17,512 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:10:18,519 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:19,726 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:20,465 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:10:20,923 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:10:23,978 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:36,543 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:41,766 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:46,174 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:10:46,891 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:53,852 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:54,236 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:10:55,312 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:00,607 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:01,626 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:03,060 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:11:04,147 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:06,401 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:11,775 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:15,569 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:11:19,170 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:21,418 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:25,860 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:31,821 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:32,280 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:11:36,512 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:11:37,800 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:37,880 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:40,276 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:11:49,848 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:52,067 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:11:56,193 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:03,165 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:03,437 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:12:17,067 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:12:17,900 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:20,356 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:12:23,285 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:23,602 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:26,481 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:28,888 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:12:30,316 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:33,123 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:33,123 - ERROR - 第231行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:38,315 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:38,808 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:12:40,059 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:42,610 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:12:47,005 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:57,130 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:12:59,337 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:13:00,118 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:13:11,272 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:13:11,272 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:13:13,541 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:13:13,905 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:13:15,029 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:13:34,545 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:13:40,797 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:13:43,012 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:13:45,659 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:13:48,239 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:13:54,300 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:04,657 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:06,863 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:08,094 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:14:14,260 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:14,628 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:14:21,534 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:21,861 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:14:33,887 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:14:36,112 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:37,607 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:39,842 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:43,014 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:14:48,090 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:54,971 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:14:55,807 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:15:02,176 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:15:05,401 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:15:13,223 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:15:15,447 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:15:21,280 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:15:21,691 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:15:27,796 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:15:29,255 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:15:31,285 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:15:37,598 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:15:52,493 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:16:19,331 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:16:23,394 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:16:30,836 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:16:34,205 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:16:47,369 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:16:53,574 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:17:10,316 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:17:20,340 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:17:21,828 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:17:26,930 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:17:28,945 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:17:29,628 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:17:35,452 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:17:43,420 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:17:45,912 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:18:08,826 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:18:15,410 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:18:17,830 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:18:18,814 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:18:22,243 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:18:22,243 - ERROR - 第491行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:18:39,715 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:18:57,336 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:18:58,127 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:19:09,031 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:19:11,779 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:19:16,090 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:19:18,729 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:19:19,614 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:19:21,831 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:19:43,929 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:19:55,372 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:19:56,955 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:19:59,365 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:00,615 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:02,671 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:03,953 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:03,954 - ERROR - 第589行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:04,891 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:11,854 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:21,559 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:27,296 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:29,753 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:30,483 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:48,016 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:50,244 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:51,557 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:51,710 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:54,122 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:54,131 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:20:58,376 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:58,376 - ERROR - 第640行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:20:58,589 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:21:00,452 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:21:11,928 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:21:23,090 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:21:24,446 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:21:25,503 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:21:30,809 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:21:33,372 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:21:42,157 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:21:57,253 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:00,327 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:01,045 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:02,766 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:06,689 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:08,313 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:08,314 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:08,415 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:08,916 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:13,134 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:13,135 - ERROR - 第707行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:16,926 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:24,177 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:24,191 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:25,687 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:25,890 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:29,139 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:31,537 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:36,252 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:36,252 - ERROR - 第731行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:37,926 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:40,342 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:42,876 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:22:48,708 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:22:49,372 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:22:55,086 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:23:03,718 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:15,561 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:17,864 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:18,095 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:23:18,312 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:23:19,281 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:20,278 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:23:24,044 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:24,517 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:24,517 - ERROR - 第774行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:25,109 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:23:27,140 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:27,551 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:23:29,363 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:29,773 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:37,558 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:23:41,357 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:44,437 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:44,881 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:23:47,918 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:52,803 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:23:58,520 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:02,237 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:03,761 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:04,867 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:07,440 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:09,083 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:09,083 - ERROR - 第811行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:11,417 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:14,222 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:14,983 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:17,662 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:18,193 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:20,606 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:22,201 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:24,823 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:24,823 - ERROR - 第829行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:24,926 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:27,929 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:31,894 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:32,642 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:34,124 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:40,807 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:42,871 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:46,618 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:46,826 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:24:49,242 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:24:51,828 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:06,026 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:17,793 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:25:18,452 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:25:20,679 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:24,484 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:25,247 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:25:25,247 - ERROR - 第884行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:25:25,708 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:32,276 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:33,963 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:36,402 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:25:42,996 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:50,331 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:50,733 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:51,973 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:52,918 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:25:52,955 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:53,815 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:25:57,365 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:25:57,366 - ERROR - 第912行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:01,751 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:26:04,841 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:26:12,606 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:15,334 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:17,610 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:25,098 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:26:26,570 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:40,527 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:40,983 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:26:43,156 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:47,008 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:26:47,743 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:47,744 - ERROR - 第952行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:26:50,435 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:00,187 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:27:02,604 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:27:06,825 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:06,825 - ERROR - 第973行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:07,049 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:07,655 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2427)')))
2025-08-15 13:27:11,122 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:15,639 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:24,530 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:24,869 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:27,780 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:28,005 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:27:36,054 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:27:38,689 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:41,188 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:27:51,079 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:53,522 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:54,801 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:27:55,927 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:27:56,741 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:27:59,157 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:28:07,158 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:28:07,358 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:28:09,727 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:15,381 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:28:17,622 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:21,249 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:28:21,901 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:30,288 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:28:38,402 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:44,036 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:28:45,087 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:48,753 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:49,273 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:51,159 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:28:51,486 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:54,003 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:28:54,208 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:55,415 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:28:55,415 - ERROR - 第1066行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:00,175 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:02,784 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:16,552 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:18,769 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:27,364 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:30,641 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:31,971 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:32,776 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:34,186 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:34,986 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:37,404 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:38,397 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:38,397 - ERROR - 第1100行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:40,467 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:42,579 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:43,039 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:43,084 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:45,215 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:29:47,623 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:29:49,269 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:30:01,378 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:03,971 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:30:05,271 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:07,666 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:10,815 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:16,020 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:30:18,547 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:20,795 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:30,819 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:30:37,361 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:38,713 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:40,074 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:30:40,805 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:41,136 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:46,690 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:49,157 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:30:50,085 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:30:56,469 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:00,347 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:31:02,571 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:31:05,953 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:31:06,407 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:06,979 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:06,979 - ERROR - 第1178行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:07,405 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:13,549 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:17,199 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:19,830 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:23,610 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:27,194 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:33,415 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:31:43,068 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:31:55,299 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:31:57,135 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:31:58,527 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:32:06,699 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:32:16,214 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:32:18,709 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:32:23,452 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:32:35,566 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:32:37,789 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:32:40,454 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:32:42,361 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:32:42,976 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:32:44,570 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:32:44,607 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:32:47,046 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:01,114 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:03,331 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:05,247 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:07,548 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:07,549 - ERROR - 第1287行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:09,396 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:09,526 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:11,628 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:16,059 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:33:16,059 - ERROR - 第1298行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:33:16,182 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:33:20,789 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:21,197 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:33:23,418 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:36,554 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:39,320 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:33:47,140 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:33:50,181 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:33:51,345 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:02,847 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:06,118 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:06,657 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:34:08,346 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:08,371 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:34:19,013 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:34:21,608 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:29,150 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:34,308 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:34:36,705 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:34:36,805 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:39,674 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:34:40,935 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:40,935 - ERROR - 第1377行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:52,373 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:52,767 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:54,767 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:55,015 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:58,977 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:58,977 - ERROR - 第1395行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:34:59,083 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:34:59,657 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:01,299 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:02,091 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:35:04,159 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:35:06,399 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:08,610 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:35:20,203 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:35:21,154 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:21,754 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:35:22,623 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:24,385 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:35:25,775 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:32,391 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:33,054 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:35:42,424 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:43,138 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:53,263 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:35:57,026 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:35:57,027 - ERROR - 第1419行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:35:57,701 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:00,550 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:01,380 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:05,215 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:05,432 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:08,656 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:10,110 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:14,421 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:15,833 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:22,200 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:22,758 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:31,388 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:32,393 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:32,721 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:33,857 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:35,729 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:38,437 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:44,776 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:47,014 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:53,027 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:54,166 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:36:56,431 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:57,437 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:36:58,170 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:05,085 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:08,587 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:11,096 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:12,708 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:14,921 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:18,292 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:19,094 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:25,789 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:26,497 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:27,216 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:28,184 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:29,431 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:41,796 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:43,741 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:46,587 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:46,771 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:49,461 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:50,124 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:51,513 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:37:53,968 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:56,154 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:56,154 - ERROR - 第1557行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:56,369 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:37:58,581 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:37:58,582 - ERROR - 第1585行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:01,945 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:07,195 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:07,758 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:08,857 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:21,408 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:23,096 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:24,407 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:25,511 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:26,622 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:29,163 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:30,149 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:31,879 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:34,333 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:37,466 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:38,843 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:39,431 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:40,528 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:47,245 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:52,740 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:54,128 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:54,964 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:38:56,197 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:56,525 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:38:57,568 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:00,676 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:00,740 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:00,740 - ERROR - 第1652行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:00,952 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:08,726 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:09,274 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:12,641 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:19,691 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:19,896 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:21,343 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:21,713 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:29,611 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:30,581 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:30,883 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:31,831 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:33,281 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:39,261 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:39:39,462 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:56,760 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:39:59,520 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:10,437 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:40:13,568 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:40:19,244 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:19,481 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:26,419 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:28,641 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:29,586 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:40:31,870 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:40:31,993 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:40:32,290 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:33,086 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:40:40,526 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:40:41,285 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:46,044 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:40:46,809 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:50,706 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:40:55,702 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:41:09,900 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:41:11,104 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:41:11,657 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:41:21,036 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:41:31,222 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:41:51,345 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:41:53,439 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:41:56,157 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:42:01,800 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:42:02,771 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:42:20,264 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:42:23,879 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:42:24,277 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:42:28,189 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:42:34,306 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:42:48,006 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:42:48,534 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:42:49,704 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:01,782 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:12,891 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:43:13,215 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:43:15,143 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:19,376 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:19,376 - ERROR - 第1900行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:19,738 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:21,960 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:23,134 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:25,360 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:28,500 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:43:28,758 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:32,327 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:37,413 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:39,098 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:43:41,202 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:41,319 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:42,521 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:43,139 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:43,428 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:44,745 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:47,468 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:48,040 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:43:48,040 - ERROR - 第1924行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:43:48,260 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:49,313 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:43:51,990 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:43:56,139 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:44:03,155 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:06,629 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:06,751 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:44:07,745 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:44:10,998 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:10,998 - ERROR - 第1931行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:23,272 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:44:25,866 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:44:32,573 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:44:38,386 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:44:40,610 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:48,126 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:48,989 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:50,425 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:44:53,434 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:56,829 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:44:59,451 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:45:01,350 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:45:01,862 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:45:03,009 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:45:03,995 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:45:04,994 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:45:16,650 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:45:27,091 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:45:28,250 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:45:29,337 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:45:37,091 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:45:39,678 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:45:41,745 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:45:44,144 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:45:48,799 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:45:48,988 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:46:04,375 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:46:15,464 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:46:18,835 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:46:19,057 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:46:53,159 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:46:54,515 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:47:04,415 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:47:04,834 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:47:15,565 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:47:17,786 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:47:33,615 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:47:55,496 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:48:04,865 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:48:05,086 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:48:05,890 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:48:07,335 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:48:08,357 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:48:21,395 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:48:22,303 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:48:41,469 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:48:44,349 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:48:48,603 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:48:57,544 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:00,610 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:05,278 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:05,384 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:07,047 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:07,291 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:21,334 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:22,679 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:29,011 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:31,251 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:31,522 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:34,791 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:36,002 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:45,868 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:49:46,755 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:56,121 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:49:56,671 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:07,615 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:50:09,828 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:18,183 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:50:21,032 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:28,533 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:50:28,843 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:50:29,074 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:50:31,231 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:32,772 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:50:34,997 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:39,857 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:50:44,271 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:50:44,272 - ERROR - 第2294行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:50:55,027 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:55,647 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:57,279 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:50:59,758 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:51:01,686 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:01,686 - ERROR - 第2308行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:02,654 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:16,238 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:30,634 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:31,284 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:51:31,914 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:44,473 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:51:46,697 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:47,312 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:47,537 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:55,898 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:56,146 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:51:58,290 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:51:58,779 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:01,453 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:06,234 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:06,491 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:06,623 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:06,623 - ERROR - 第2358行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:10,147 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:10,324 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:10,795 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:12,565 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:15,178 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:15,271 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:17,596 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:17,740 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:18,027 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:21,810 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:21,810 - ERROR - 第2373行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:22,055 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:22,581 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:27,879 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:38,300 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:42,085 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:42,788 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:52:46,458 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:51,570 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:52:56,113 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:00,150 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:00,813 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:01,141 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:01,937 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:02,109 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:08,174 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:13,897 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:16,230 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:17,839 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:18,375 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:20,058 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:20,776 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:21,595 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:23,815 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:23,881 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:24,992 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:24,992 - ERROR - 第2428行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:25,227 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:26,094 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:26,536 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:35,660 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:38,239 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:53:41,869 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:41,921 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:47,176 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:49,428 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:53,645 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:53,646 - ERROR - 第2454行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:53,795 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:53:57,923 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:08,702 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:12,485 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:13,650 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:17,002 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:18,625 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:19,251 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:19,682 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:23,465 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:23,465 - ERROR - 第2479行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:23,853 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:26,075 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:26,340 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:29,085 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:42,167 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:45,765 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:46,511 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:47,987 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:54:54,307 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:54:57,828 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:07,605 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:08,565 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:12,627 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:14,701 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:18,123 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:18,472 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:20,548 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:26,158 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:27,678 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:33,114 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:33,961 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:44,401 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:47,705 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:55:49,259 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:50,155 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:53,128 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:53,264 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:55,366 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:55:59,798 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:55:59,798 - ERROR - 第2554行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:56:00,185 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:56:04,348 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:56:10,669 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:56:12,590 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:56:15,168 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:56:22,312 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:56:31,825 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:56:32,636 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:56:43,533 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:56:51,755 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:01,802 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:02,125 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:04,239 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:05,104 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:05,985 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:10,198 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:10,198 - ERROR - 第2590行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:12,891 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:12,953 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:12,968 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:15,449 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:57:15,989 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:19,663 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:19,663 - ERROR - 第2595行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:24,161 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:34,260 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:34,539 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:37,503 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:38,483 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:38,483 - ERROR - 第2587行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:38,649 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:38,649 - ERROR - 第2586行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:57:39,140 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:57:39,141 - ERROR - 第2589行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:57:42,090 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:56,961 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:57:58,057 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:58:00,956 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:58:08,468 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:58:10,559 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:58:13,084 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 13:58:28,322 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:58:41,002 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:58:46,478 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:58:48,705 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:58:52,925 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:58:52,925 - ERROR - 第2646行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:07,017 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:59:18,412 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:18,696 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:21,129 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:35,439 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:37,538 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:46,835 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:59:49,381 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 13:59:56,279 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:57,472 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:58,426 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 13:59:59,690 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:00,993 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:00:02,122 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:03,159 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:33,215 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:34,667 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:35,436 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:35,673 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:00:35,719 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:00:35,720 - ERROR - 第2710行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:00:37,110 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:00:39,521 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:00:39,649 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:39,649 - ERROR - 第2726行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:39,883 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:00:40,485 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:00:42,283 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:00:43,128 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:53,216 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:55,609 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:00:57,890 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:00,299 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:10,044 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:10,638 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:11,588 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:12,252 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:12,862 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:16,159 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:17,093 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:17,093 - ERROR - 第2773行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:17,309 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:18,858 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:23,550 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:23,862 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:26,080 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:26,350 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:27,459 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:27,936 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:28,329 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:28,746 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:28,878 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:30,042 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:30,162 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:30,671 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:30,671 - ERROR - 第2794行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:33,139 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:33,139 - ERROR - 第2799行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:33,940 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:38,098 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:39,040 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:39,392 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:41,446 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:41,503 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:01:49,429 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:01:51,652 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:01,823 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:06,342 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:07,260 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:02:08,072 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:02:11,300 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:02:11,405 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:12,753 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:13,070 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:15,280 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:17,465 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:18,877 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:19,484 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:19,485 - ERROR - 第2862行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:20,492 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:21,739 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:02:25,248 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:02:26,288 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:28,524 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:28,681 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:02:30,389 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:33,340 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:37,100 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:02:38,735 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:43,155 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:48,379 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:02:52,538 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:02:54,186 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:02:56,570 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:00,247 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:04,021 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:06,458 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:06,642 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:03:09,259 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:09,679 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:03:09,831 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:12,277 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:03:13,630 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:33,348 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:03:48,372 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:03:50,751 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:03:53,200 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:03:55,778 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:15,410 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:04:29,997 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:04:40,369 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:49,858 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:04:51,190 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:53,410 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:04:56,842 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:57,825 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:57,825 - ERROR - 第3018行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:58,231 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:59,289 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:04:59,378 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:05:03,905 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:05:03,905 - ERROR - 第3023行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:05:04,144 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:05:04,587 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:05:21,685 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:05:26,160 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:05:26,844 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:05:29,220 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:05:30,434 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:05:38,849 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:05:39,318 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:05:42,562 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:05:44,739 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:11,160 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:15,067 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:06:22,559 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:06:24,774 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:26,759 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:06:29,234 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:29,234 - ERROR - 第3080行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:40,026 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:40,609 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:42,843 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:42,882 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:06:51,546 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:53,239 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:54,138 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:06:55,451 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:06:57,435 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:00,746 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:03,332 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:07:04,309 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:05,345 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:19,956 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:07:22,818 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:24,596 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:27,030 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:07:29,912 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:07:30,057 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:31,258 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:31,258 - ERROR - 第3134行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:31,857 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:39,507 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:42,320 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:43,109 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:07:46,540 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:07:47,973 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:49,507 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:07:52,244 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:53,874 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:55,499 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:55,633 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:07:55,739 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:07:56,275 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:07:59,687 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:00,715 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:00,719 - ERROR - 第3166行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:01,900 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:02,397 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:03,015 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:03,521 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:04,800 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:07,607 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:10,213 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:12,511 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:15,119 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:16,988 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:17,396 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:19,019 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:19,367 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:19,367 - ERROR - 第3187行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:19,428 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:19,505 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:22,558 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:22,558 - ERROR - 第3175行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:24,344 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:27,666 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:30,044 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:35,571 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:37,183 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:42,946 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:43,450 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:44,327 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:08:44,580 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:45,656 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:47,804 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:48,116 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:52,814 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:57,242 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:57,455 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:58,314 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:08:59,461 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:01,248 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:02,622 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:05,028 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:06,306 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:08,515 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:08,566 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:09,252 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:09,252 - ERROR - 第3254行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:14,790 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:17,589 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:17,824 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:18,933 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:20,057 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2427)')))
2025-08-15 14:09:21,610 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:23,750 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:24,980 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:25,832 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:25,833 - ERROR - 第3274行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:27,950 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:33,164 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:41,353 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:42,266 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:44,597 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:47,555 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:09:47,800 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:09:53,537 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:01,989 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:10:02,232 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:10:03,346 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:10:10,069 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:11,694 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:10:12,994 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:15,206 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:15,572 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:15,810 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:10:18,551 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:22,626 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:24,583 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:24,850 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:27,038 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:10:49,983 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:10:52,202 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:10:54,888 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:00,516 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:00,989 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:11:01,935 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:03,577 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:04,161 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:13,831 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:13,842 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:17,628 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:23,338 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:26,135 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:26,267 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:28,596 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:29,360 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:31,586 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:34,537 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:35,776 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:35,808 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:35,809 - ERROR - 第3439行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:38,777 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:41,129 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:41,224 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:44,839 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:47,792 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:48,043 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:50,449 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:51,324 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:51,915 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:54,220 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:11:54,415 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:11:56,444 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:11:56,833 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:06,067 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:11,463 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:12,663 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:22,654 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:24,705 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:25,548 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:31,406 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:33,627 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:35,169 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:38,212 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:12:41,042 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:42,053 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:42,106 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:45,174 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:45,987 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:55,084 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:55,782 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:12:56,664 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:12:57,303 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:13:03,712 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:13:09,177 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:13:10,088 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:13:11,795 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:13:32,699 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:13:43,371 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:13:55,952 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:13:56,524 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:13:58,930 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:14:03,047 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:05,255 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:07,476 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:13,392 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:18,712 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:14:19,855 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:14:28,562 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:35,675 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:41,178 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:44,197 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:48,686 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:14:59,911 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:04,502 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:09,787 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:15:11,998 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:13,828 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:15,908 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:15:16,252 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:15:18,317 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:24,410 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:27,794 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:15:36,669 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:37,166 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:39,615 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:40,330 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:15:49,795 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:15:50,009 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:15:51,192 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:00,511 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:01,164 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:16:04,767 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:05,220 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:05,806 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:07,044 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:09,152 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:16:17,797 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:16:23,460 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:25,892 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:16:36,691 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:45,370 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:46,568 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:16:48,376 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:16:58,506 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:16:58,691 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2427)')))
2025-08-15 14:17:14,993 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:17:16,991 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:17:23,740 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:17:24,416 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:17:27,393 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:17:37,049 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:17:45,365 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:17:51,844 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:18:05,703 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:18:12,659 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:18:19,690 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:18:20,928 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:18:22,676 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:18:28,554 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:18:33,683 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:18:54,727 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:19:06,096 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:19:14,063 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:19:21,372 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:19:35,635 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:19:46,666 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:19:59,382 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:20:00,099 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:20:03,071 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:20:16,298 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:20:29,942 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:20:40,514 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:20:45,549 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:20:46,485 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:20:51,631 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:20:53,467 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:20:58,794 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:21:00,175 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:21:01,660 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:21:02,120 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:21:06,763 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:21:13,977 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:21:14,494 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:21:16,114 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:21:19,307 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:21:21,550 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:21:25,994 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:21:30,745 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:21:52,934 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:21:58,947 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:22:02,716 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:22:24,017 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:22:24,210 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:22:28,229 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:22:32,600 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:22:45,031 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:22:45,416 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:22:47,247 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:22:47,821 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:23:00,637 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:23:02,219 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:23:03,381 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:23:13,311 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:23:22,273 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:23:22,273 - ERROR - 第4261行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:23:29,398 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:23:36,887 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:23:48,712 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:23:55,781 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:23:58,705 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:24:01,712 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:24:03,424 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:24:05,648 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:24:07,914 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:24:10,123 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:24:11,178 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:24:13,400 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:24:38,642 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:24:59,576 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:25:01,788 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:25:05,433 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:25:11,359 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:25:22,959 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:25:31,596 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:25:33,901 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:25:35,377 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:25:50,906 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:25:53,496 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:25:56,067 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:03,518 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:26:05,744 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:07,991 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:12,304 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:14,308 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:14,577 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:26:22,079 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:26:25,561 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:26,250 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:31,408 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:31,447 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:33,622 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:51,304 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:51,363 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:26:58,586 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:27:01,176 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:27:08,191 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:27:10,405 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:27:11,735 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:27:16,515 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:27:29,727 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:27:52,752 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:27:55,890 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:27:58,116 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:28:14,125 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:28:28,217 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:28:37,501 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:28:39,436 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:28:44,550 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:29:31,778 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:29:34,418 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:29:42,809 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:29:44,396 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:30:03,848 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:30:21,284 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:30:23,399 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:30:28,366 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:30:30,582 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:30:31,058 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:30:35,921 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:30:43,716 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:30:53,342 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:30:58,354 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:31:00,942 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:31:05,354 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:31:05,355 - ERROR - 第4796行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:31:11,376 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:31:21,274 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:31:27,168 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:31:34,661 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:31:54,618 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:31:57,914 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:32:09,580 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:32:20,566 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:33:01,968 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:33:05,739 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:33:08,011 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:33:13,835 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:33:14,683 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:33:17,163 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:33:39,109 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:34:07,780 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:34:16,256 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:34:38,670 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:34:39,360 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:35:12,858 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:21,242 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:35:21,997 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:23,733 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:35:38,052 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:39,502 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:50,894 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:53,497 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:53,695 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:54,145 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:54,160 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:54,282 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:54,435 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:35:55,736 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:35:56,146 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:36:04,912 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:36:10,466 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:36:11,918 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:36:14,713 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:36:14,714 - ERROR - 第4982行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:36:49,904 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:36:53,127 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:36:55,879 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:37:04,622 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:37:05,590 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:37:06,010 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:37:11,666 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:37:28,565 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:37:32,496 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:37:35,784 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:37:36,371 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:37:46,281 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:37:56,483 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:38:01,696 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:38:04,280 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:38:10,369 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:38:19,093 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:38:22,458 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:38:27,369 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:38:44,257 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:38:46,181 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:38:48,436 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:38:53,787 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:38:55,395 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:00,200 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:39:04,776 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:07,699 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:39:12,320 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:39:18,127 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:18,842 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:20,342 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:25,481 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:34,136 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:39:34,907 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:39:38,854 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:39:42,563 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:44,124 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:44,575 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:39:46,005 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:39:48,420 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:39:48,571 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:00,534 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:03,648 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:06,693 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:08,469 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:10,685 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:19,021 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:19,318 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:22,075 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:39,512 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:40,889 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:44,801 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:45,106 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:40:45,107 - ERROR - 第5206行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:40:45,432 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:47,833 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:51,769 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:52,234 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:52,235 - ERROR - 第5241行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:52,456 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:53,492 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:40:54,860 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:40:59,876 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:41:01,950 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:04,173 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:07,976 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:10,197 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:20,805 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:41:21,694 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:23,032 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:24,151 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:41:26,369 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:34,908 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:41:38,015 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:39,842 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:41:50,012 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:41:52,159 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:52,251 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:53,338 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:54,369 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:55,573 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:41:59,021 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:42:01,248 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:42:03,396 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:42:11,240 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:42:17,876 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:42:43,672 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:44,273 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:46,729 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:47,704 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:48,442 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:48,951 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:42:49,517 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:50,354 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:53,347 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:42:53,347 - ERROR - 第5328行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:42:56,387 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:56,542 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:42:59,167 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:43:03,913 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:43:19,775 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:43:21,669 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:43:21,999 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:43:23,389 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:43:26,598 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:43:26,598 - ERROR - 第5357行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:43:29,009 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:43:29,114 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:43:47,195 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:43:48,119 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:43:59,353 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:44:17,526 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:44:20,125 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:44:23,045 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:44:24,349 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:44:24,349 - ERROR - 第5385行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:44:37,766 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:44:38,271 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:44:40,165 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:44:41,582 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:44:41,738 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:44:47,879 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:44:50,111 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:44:52,556 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:44:53,572 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:45:19,379 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:45:19,477 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:21,105 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:33,017 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:37,585 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:45:38,273 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:39,566 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:45:41,790 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:45:44,649 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:45:47,690 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:51,980 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:56,579 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:57,846 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:58,855 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:45:59,013 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:00,092 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:07,834 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:46:17,958 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:46:22,713 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:25,820 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:46:30,034 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:30,034 - ERROR - 第5522行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:34,412 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:34,532 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:46:34,533 - ERROR - 第5504行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:46:42,800 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:46:45,033 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:53,029 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:55,241 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:46:55,376 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:46:58,024 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:47:06,035 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:47:10,286 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:47:11,994 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:13,906 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:15,361 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:16,052 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:47:18,933 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:22,413 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:38,044 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:39,494 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:40,280 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:44,795 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:47:45,972 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:47:50,761 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:48:01,564 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:48:13,600 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:48:16,827 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:48:24,532 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:48:35,854 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:48:36,140 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:48:36,179 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:48:37,204 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:48:42,199 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:48:48,184 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:48:49,782 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:48:50,400 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:48:51,579 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:48:52,045 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:48:57,781 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:02,663 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:49:04,445 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:04,887 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:10,072 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:49:11,374 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:49:12,245 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:13,466 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:49:17,179 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:49:19,422 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:22,996 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:26,938 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:49:30,532 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:49:36,302 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:38,121 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:49:48,737 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:49:58,622 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:50:01,619 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:50:03,537 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:50:04,355 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:50:05,714 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:50:08,704 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:50:13,950 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:50:16,486 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:50:18,697 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:50:36,815 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:37,910 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:38,141 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:40,754 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:40,800 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:41,108 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:43,037 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:50:44,563 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:46,412 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:46,783 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:50:46,998 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:53,141 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:53,142 - ERROR - 第5727行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:50:53,373 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:51:00,014 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:51:03,563 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:51:11,225 - ERROR - 分析内容时发生错误 (尝试3/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:51:11,226 - ERROR - 第5718行分析失败: 分析错误: HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:51:37,340 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:51:51,671 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:51:55,449 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:52:05,154 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:07,264 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:13,339 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:32,989 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:52:39,576 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:42,264 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:52:44,787 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:46,809 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:47,813 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:59,108 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:52:59,108 - ERROR - 第5829行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:53:14,262 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:53:15,330 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:53:17,121 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:53:20,238 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:53:21,712 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:53:24,267 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:53:24,637 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:53:24,638 - ERROR - 第5834行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:53:35,140 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:53:44,146 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:53:44,274 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:53:47,076 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:53:55,037 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:53:57,264 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:53:59,098 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:09,329 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:10,364 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:10,776 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:12,581 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:13,183 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:13,536 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:13,828 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:16,295 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:16,481 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:17,634 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:17,635 - ERROR - 第5886行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:22,690 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:23,463 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:38,314 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:49,337 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:54:54,279 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:56,826 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:54:58,688 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:55:24,572 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:55:25,025 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:55:26,722 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:55:27,849 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:55:28,901 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:55:30,755 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:55:31,119 - ERROR - 分析内容时发生错误 (尝试2/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:55:36,970 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:55:53,117 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:56:07,881 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:56:08,114 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:56:15,631 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:56:17,884 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:56:18,001 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:56:22,100 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:56:22,102 - ERROR - 第5969行分析失败: 分析错误: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:56:31,048 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:56:36,418 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:56:36,680 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:56:38,832 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:56:40,325 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:56:44,216 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2427)')))
2025-08-15 14:56:50,095 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:57:17,458 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:57:26,913 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:57:27,309 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:57:29,530 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:57:32,367 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:57:40,136 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:57:43,823 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:57:45,834 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:57:47,588 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:57:49,772 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:57:49,806 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:57:50,193 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:57:54,219 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:57:54,219 - ERROR - 第6039行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:57:54,586 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:57:59,509 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:58:05,068 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:58:13,229 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:58:22,071 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:58:24,286 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:58:26,895 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:58:31,295 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:58:31,722 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:58:53,040 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:58:55,431 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:58:56,701 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:58:58,013 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:59:17,680 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:59:26,956 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 14:59:29,394 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:59:31,858 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:59:36,273 - ERROR - 分析内容时发生错误 (尝试3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:59:36,273 - ERROR - 第6124行分析失败: 分析错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 14:59:43,929 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:59:48,451 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 14:59:50,869 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 15:00:33,455 - ERROR - 分析内容时发生错误 (尝试1/3): HTTPSConnectionPool(host='one-api.zaoniao.vip', port=443): Read timed out. (read timeout=30)
2025-08-15 15:15:28,717 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:15:28,831 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 15:15:37,720 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-15 15:20:41,660 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:21:04,623 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:21:06,843 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:21:23,681 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:21:24,303 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:21:26,302 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:21:48,153 - ERROR - 分析内容时发生错误 (尝试1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-15 15:21:50,753 - ERROR - 分析内容时发生错误 (尝试2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
