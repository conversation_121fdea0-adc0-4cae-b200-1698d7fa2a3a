#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重试失败记录的分析脚本
"""

import pandas as pd
import os
import sys
from sensitive_content_analyzer import SensitiveContentAnalyzer, setup_logging

def main():
    """重试失败记录的主函数"""
    logger = setup_logging()
    
    # 检查失败记录文件是否存在
    failed_csv_file = "敏感内容分析结果_失败记录.csv"
    if not os.path.exists(failed_csv_file):
        print("未找到失败记录文件，无需重试")
        return
    
    # 读取失败记录
    try:
        failed_df = pd.read_csv(failed_csv_file, encoding='utf-8-sig')
        print(f"找到{len(failed_df)}条失败记录")
    except Exception as e:
        print(f"读取失败记录文件时发生错误: {e}")
        return
    
    if len(failed_df) == 0:
        print("失败记录文件为空")
        return
    
    # API配置
    API_URL = "https://one-api.zaoniao.vip"
    API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
    MODEL = "gpt-5-nano-2025-08-07"
    
    # 创建分析器实例
    analyzer = SensitiveContentAnalyzer(API_URL, API_KEY, MODEL)
    
    # 重试结果
    retry_success = []
    retry_failed = []
    
    print("开始重试失败记录...")
    
    for index, row in failed_df.iterrows():
        content = str(row['发言内容'])
        user_id = row['id']
        
        print(f"重试第{index+1}/{len(failed_df)}条记录 (ID: {user_id})")
        
        # 重新分析
        analysis = analyzer.analyze_content(content, max_retries=5)  # 增加重试次数
        
        if not analysis.get('error', False):
            # 成功分析
            if analysis.get('is_sensitive', False):
                sensitive_data = {
                    'id': user_id,
                    '敏感言论': content,
                    '时间': row['时间'],
                    '群组': row['群组'],
                    '敏感类型': analysis.get('category', '其他敏感'),
                    '敏感程度': analysis.get('severity', 1)
                }
                retry_success.append(sensitive_data)
                logger.info(f"重试成功 - ID {user_id}: {analysis.get('category')}")
            else:
                logger.info(f"重试成功 - ID {user_id}: 无敏感内容")
        else:
            # 仍然失败
            retry_failed.append(row.to_dict())
            logger.error(f"重试失败 - ID {user_id}: {analysis.get('reason')}")
    
    # 保存重试结果
    if retry_success:
        success_df = pd.DataFrame(retry_success)
        success_file = "重试成功记录.xlsx"
        success_df.to_excel(success_file, index=False)
        print(f"\n重试成功{len(retry_success)}条记录，已保存到: {success_file}")
    
    if retry_failed:
        failed_df_new = pd.DataFrame(retry_failed)
        failed_file = "仍然失败记录.csv"
        failed_df_new.to_csv(failed_file, index=False, encoding='utf-8-sig')
        print(f"仍有{len(retry_failed)}条记录失败，已保存到: {failed_file}")
    else:
        print("所有失败记录都已成功重试！")
    
    print(f"\n=== 重试完成 ===")
    print(f"原失败记录: {len(failed_df)}条")
    print(f"重试成功: {len(retry_success)}条")
    print(f"仍然失败: {len(retry_failed)}条")

if __name__ == "__main__":
    main()
