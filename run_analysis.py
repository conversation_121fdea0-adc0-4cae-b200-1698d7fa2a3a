#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感内容分析工具 - 简化运行版本
"""

import subprocess
import sys
import os

def get_input_file():
    """获取用户输入的文件名"""
    print("=== 敏感内容分析工具 ===")
    print("此工具将分析Excel文件中的敏感内容并生成分析报告")
    print("\n📁 请指定要分析的Excel文件:")
    print("示例文件名: 聊天记录.xlsx, 群组消息.xlsx, data.xlsx")
    print("注意: 文件应包含Sheet1工作表，且包含A、B、C、D列数据")

    while True:
        filename = input("\n请输入Excel文件名 (默认: 5.xlsx): ").strip()

        # 如果用户直接回车，使用默认文件名
        if not filename:
            filename = "5.xlsx"

        # 自动添加.xlsx扩展名（如果用户没有输入）
        if not filename.lower().endswith('.xlsx'):
            filename += '.xlsx'

        # 检查文件是否存在
        if os.path.exists(filename):
            print(f"✅ 找到文件: {filename}")
            return filename
        else:
            print(f"❌ 文件不存在: {filename}")
            print("请检查文件名是否正确，或将文件放在当前目录中")
            retry = input("是否重新输入? (y/n): ").strip().lower()
            if retry != 'y':
                return None

def main():
    # 获取输入文件
    input_file = get_input_file()
    if not input_file:
        print("程序退出")
        return

    print(f"\n📊 将分析文件: {input_file}")
    
    # 询问用户选择
    print("请选择分析模式:")
    print("1. 测试模式 (分析前50行)")
    print("2. 小规模分析 (分析前500行)")
    print("3. 完整分析 (分析全部数据，可能需要较长时间)")
    print()
    
    while True:
        choice = input("请输入选择 (1/2/3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("无效选择，请输入 1、2 或 3")
    
    # 设置分析行数
    if choice == '1':
        max_rows = 50
        print(f"选择测试模式，将分析前 {max_rows} 行数据")
    elif choice == '2':
        max_rows = 500
        print(f"选择小规模分析，将分析前 {max_rows} 行数据")
    else:
        max_rows = None
        print("选择完整分析，将分析全部数据")
        print("注意: 完整分析可能需要数小时时间，请耐心等待")
    
    print()
    try:
        confirm = input("确认开始分析? (y/n): ").strip().lower()
        if confirm != 'y':
            print("分析已取消")
            return
    except EOFError:
        # 如果是通过管道输入，默认确认
        print("自动确认开始分析...")
        pass
    
    # 运行分析脚本
    try:
        if max_rows:
            # 使用echo传递文件名和行数限制
            cmd = f'echo {input_file} && echo {max_rows} | python sensitive_content_analyzer.py'
        else:
            # 传递文件名，直接回车表示分析全部
            cmd = f'echo {input_file} && echo "" | python sensitive_content_analyzer.py'
        
        print("开始分析...")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        # 显示输出
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误信息:", result.stderr)
        
        if result.returncode == 0:
            print("\n🎉 分析完成！")
            print("\n📊 生成的文件:")
            print("- 敏感内容分析结果.xlsx (主要结果文件)")
            print("- analysis.log (完整分析日志)")
            print("- error.log (错误日志)")
            print("- 敏感内容分析结果_失败记录.csv (如有失败记录)")

            print("\n📋 结果文件包含的工作表:")
            print("- '涉政言论': 政治敏感内容")
            print("- '其他敏感内容': 其他类型敏感内容汇总")
            print("- 各分类工作表: 色情、暴力、歧视等详细分类")
            print("- '分析失败记录': 分析失败的记录(如有)")

            print("\n💡 后续操作:")
            print("- 如有失败记录，运行: python retry_failed.py")
            print("- 查看日志文件了解详细分析过程")
        else:
            print(f"\n❌ 分析失败，返回码: {result.returncode}")
            print("请检查error.log文件了解详细错误信息")
            
    except Exception as e:
        print(f"运行分析时发生错误: {e}")

if __name__ == "__main__":
    main()
