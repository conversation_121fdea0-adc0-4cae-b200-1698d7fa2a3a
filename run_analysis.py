#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感内容分析工具 - 简化运行版本
"""

import subprocess
import sys
import os

def main():
    print("=== 敏感内容分析工具 ===")
    print("此工具将分析Excel文件中的敏感内容并生成分析报告")
    print()
    
    # 检查文件是否存在
    if not os.path.exists("5.xlsx"):
        print("错误: 找不到文件 '5.xlsx'")
        print("请确保Excel文件存在于当前目录中")
        return
    
    # 询问用户选择
    print("请选择分析模式:")
    print("1. 测试模式 (分析前50行)")
    print("2. 小规模分析 (分析前500行)")
    print("3. 完整分析 (分析全部数据，可能需要较长时间)")
    print()
    
    while True:
        choice = input("请输入选择 (1/2/3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("无效选择，请输入 1、2 或 3")
    
    # 设置分析行数
    if choice == '1':
        max_rows = 50
        print(f"选择测试模式，将分析前 {max_rows} 行数据")
    elif choice == '2':
        max_rows = 500
        print(f"选择小规模分析，将分析前 {max_rows} 行数据")
    else:
        max_rows = None
        print("选择完整分析，将分析全部数据")
        print("注意: 完整分析可能需要数小时时间，请耐心等待")
    
    print()
    try:
        confirm = input("确认开始分析? (y/n): ").strip().lower()
        if confirm != 'y':
            print("分析已取消")
            return
    except EOFError:
        # 如果是通过管道输入，默认确认
        print("自动确认开始分析...")
        pass
    
    # 运行分析脚本
    try:
        if max_rows:
            # 使用echo传递行数限制
            cmd = f'echo {max_rows} | python sensitive_content_analyzer.py'
        else:
            # 直接回车表示分析全部
            cmd = 'echo "" | python sensitive_content_analyzer.py'
        
        print("开始分析...")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        # 显示输出
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误信息:", result.stderr)
        
        if result.returncode == 0:
            print("\n分析完成！")
            print("结果文件: 敏感内容分析结果.xlsx")
            print("该文件包含两个工作表:")
            print("- '涉政言论': 包含政治敏感内容")
            print("- '其他敏感内容': 包含其他类型敏感内容")
        else:
            print(f"\n分析失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"运行分析时发生错误: {e}")

if __name__ == "__main__":
    main()
