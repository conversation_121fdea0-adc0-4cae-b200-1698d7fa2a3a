#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感内容分析工具 - 简化运行版本
"""

import sys
import os

def get_input_file():
    """获取用户输入的文件名"""
    print("=== 敏感内容分析工具 ===")
    print("此工具将分析Excel文件中的敏感内容并生成分析报告")
    print("\n请指定要分析的Excel文件:")
    print("示例文件名: 聊天记录.xlsx, 群组消息.xlsx, data.xlsx")
    print("注意: 文件应包含Sheet1工作表，且包含A、B、C、D列数据")

    while True:
        filename = input("\n请输入Excel文件名 (默认: 5.xlsx): ").strip()

        # 如果用户直接回车，使用默认文件名
        if not filename:
            filename = "5.xlsx"

        # 自动添加.xlsx扩展名（如果用户没有输入）
        if not filename.lower().endswith('.xlsx'):
            filename += '.xlsx'

        # 检查文件是否存在
        if os.path.exists(filename):
            print(f"成功: 找到文件: {filename}")
            return filename
        else:
            print(f"错误: 文件不存在: {filename}")
            print("请检查文件名是否正确，或将文件放在当前目录中")
            retry = input("是否重新输入? (y/n): ").strip().lower()
            if retry != 'y':
                return None

def main():
    # 获取输入文件
    input_file = get_input_file()
    if not input_file:
        print("程序退出")
        return

    print(f"\n分析配置: 将分析文件: {input_file}")

    # 询问用户选择处理方式
    print("请选择处理方式:")
    print("1. 单线程处理 (稳定，适合小数据量)")
    print("2. 多线程处理 (快速，适合大数据量)")
    print()

    while True:
        thread_choice = input("请输入选择 (1/2): ").strip()
        if thread_choice in ['1', '2']:
            break
        print("无效选择，请输入 1 或 2")

    use_multithread = thread_choice == '2'

    if use_multithread:
        print("选择多线程处理模式")
        try:
            thread_count = input("请输入并发线程数 (1-10，默认: 3): ").strip()
            max_workers = int(thread_count) if thread_count else 3
            max_workers = max(1, min(10, max_workers))
            print(f"将使用 {max_workers} 个并发线程")
        except ValueError:
            max_workers = 3
            print("输入无效，使用默认线程数: 3")
    else:
        print("选择单线程处理模式")
        max_workers = 1

    # 询问用户选择分析规模
    print("\n请选择分析模式:")
    print("1. 测试模式 (分析前50行)")
    print("2. 小规模分析 (分析前500行)")
    print("3. 完整分析 (分析全部数据，可能需要较长时间)")
    print()
    
    while True:
        choice = input("请输入选择 (1/2/3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("无效选择，请输入 1、2 或 3")

    # 设置分析行数
    if choice == '1':
        max_rows = 50
        print(f"选择测试模式，将分析前 {max_rows} 行数据")
    elif choice == '2':
        max_rows = 500
        print(f"选择小规模分析，将分析前 {max_rows} 行数据")
    else:
        max_rows = None
        print("选择完整分析，将分析全部数据")
        if use_multithread:
            print("注意: 多线程完整分析速度较快，但请确保网络稳定")
        else:
            print("注意: 单线程完整分析可能需要数小时时间，请耐心等待")
    
    print()
    try:
        confirm = input("确认开始分析? (y/n): ").strip().lower()
        if confirm != 'y':
            print("分析已取消")
            return
    except EOFError:
        # 如果是通过管道输入，默认确认
        print("自动确认开始分析...")
        pass
    
    # 运行分析脚本
    try:
        print("开始分析...")
        print("正在启动分析引擎...")

        # 直接导入和调用分析函数
        from sensitive_content_analyzer import SensitiveContentAnalyzer

        # API配置
        API_URL = "https://one-api.zaoniao.vip"
        API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"
        MODEL = "gpt-5-nano-2025-08-07"

        # 生成输出文件名
        base_name = input_file.replace('.xlsx', '')
        output_file = f"{base_name}_敏感内容分析结果.xlsx"

        print("正在处理数据，请稍候...")

        # 创建分析器实例
        analyzer = SensitiveContentAnalyzer(API_URL, API_KEY, MODEL)

        # 处理Excel文件
        political_df, other_all_df, categorized_dfs, failed_df = analyzer.process_excel_file(input_file, max_rows)

        # 保存结果
        analyzer.save_results(political_df, other_all_df, categorized_dfs, failed_df, output_file)

        print("分析引擎执行完成，正在处理结果...")

        # 模拟成功的返回码
        success = True

        if success:
            print("\n" + "="*60)
            print("                   分析任务完成！")
            print("="*60)

            # 生成实际的文件名
            base_name = input_file.replace('.xlsx', '')
            actual_output_file = f"{base_name}_敏感内容分析结果.xlsx"

            print(f"\n生成的文件:")
            print(f"- {actual_output_file} (主要结果文件)")
            print(f"- analysis.log (完整分析日志)")
            print(f"- error.log (错误日志)")

            # 检查是否有失败记录文件
            failed_file = f"{base_name}_敏感内容分析结果_失败记录.csv"
            if os.path.exists(failed_file):
                print(f"- {failed_file} (失败记录)")

            print(f"\n结果文件包含的工作表:")
            print(f"- '涉政言论': 政治敏感内容")
            print(f"- '其他敏感内容': 其他类型敏感内容汇总")
            print(f"- 各分类工作表: 色情、暴力、歧视等详细分类")
            if os.path.exists(failed_file):
                print(f"- '分析失败记录': 分析失败的记录")

            print(f"\n后续操作:")
            if os.path.exists(failed_file):
                print(f"- 有失败记录，可运行: python retry_failed.py")
            print(f"- 查看日志文件了解详细分析过程")
            print(f"- 打开 {actual_output_file} 查看分析结果")

            print("\n" + "="*60)
            print("                 感谢使用本工具！")
            print("="*60)
        else:
            print(f"\n" + "="*60)
            print("                   分析失败！")
            print("="*60)
            print("请检查error.log文件了解详细错误信息")
            print("或重新运行程序重试")
            
    except Exception as e:
        print(f"\n" + "="*60)
        print("                   分析失败！")
        print("="*60)
        print(f"运行分析时发生错误: {e}")
        print("请检查error.log文件了解详细错误信息")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
