# 敏感内容分析工具

这个Python脚本用于分析Excel文件中的敏感内容，使用大模型API进行智能识别和分类。

## 功能特点

- 读取Excel文件的Sheet1表格
- 调用大模型API分析D列的发言内容
- 智能识别敏感信息并详细分类（涉政言论、色情内容、暴力内容等9大类）
- 敏感程度评分（1-10级）
- 同ID去重（保留最敏感的一条言论）
- **多线程并发处理**（支持1-10个并发线程，大幅提升处理速度）
- 自动重试机制（API调用失败时自动重试）
- 完整的日志记录（普通日志+错误日志）
- 失败记录单独存储，支持后续重试
- 支持测试模式和完整分析模式
- 实时进度显示和详细处理信息

## 文件说明

- `sensitive_content_analyzer.py` - 主分析脚本（单线程版本）
- `sensitive_content_analyzer_mt.py` - 多线程分析脚本
- `run_analysis.py` - 简化运行脚本（推荐使用，支持选择单/多线程）
- `retry_failed.py` - 重试失败记录脚本
- `performance_test.py` - 性能测试脚本
- `requirements.txt` - 依赖包列表
- `运行分析.bat` - Windows一键运行脚本
## 运行后生成的文件

- `[文件名]_敏感内容分析结果.xlsx` - 输出结果文件
- `analysis.log` - 分析日志文件
- `error.log` - 错误日志文件
- `[文件名]_敏感内容分析结果_失败记录.csv` - 失败记录文件（如有）

注：`[文件名]`为输入文件的名称（不含扩展名）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：命令行运行

```bash
python run_analysis.py
```

然后按照提示选择分析模式：
- 测试模式：分析前50行数据
- 小规模分析：分析前500行数据
- 完整分析：分析全部数据

### 方法二：直接运行脚本

**单线程版本**（稳定，适合小数据量）：
```bash
python sensitive_content_analyzer.py
```

**多线程版本**（快速，适合大数据量）：
```bash
python sensitive_content_analyzer_mt.py
```

### 方法四：性能测试

```bash
python performance_test.py
```

## 数据格式要求

Excel文件应包含Sheet1工作表，数据格式如下：
- A列：ID信息
- B列：群组信息
- C列：时间信息
- D列：发言内容（待分析）
- E列：其他信息（可选）

支持任意文件名，如：`聊天记录.xlsx`、`群组消息.xlsx`、`data.xlsx`等

## 输出结果

脚本会生成`敏感内容分析结果.xlsx`文件，包含多个工作表：
- `涉政言论`：包含政治敏感内容
- `其他敏感内容`：包含所有其他类型敏感内容的汇总
- `色情内容`、`暴力内容`、`歧视言论`等：各类型敏感内容的详细分类
- `分析失败记录`：API调用失败的记录（如有）

每个工作表包含以下列：
- id：原A列信息
- 敏感言论：原D列信息
- 时间：原C列信息
- 群组：原B列信息
- 敏感类型：敏感内容的具体分类
- 敏感程度：1-10的敏感程度评分

## 日志文件

- `analysis.log`：完整的分析日志
- `error.log`：错误和警告日志
- `敏感内容分析结果_失败记录.csv`：分析失败的记录（如有）

## API配置

脚本使用以下API配置：
- API地址：https://one-api.zaoniao.vip
- 模型：gpt-5-nano-2025-08-07
- Token：已内置在脚本中

## 测试API连接

如果遇到连接问题，可以运行主脚本的测试模式来验证API连接。

## 多线程使用建议

### 线程数选择
- **小数据量（<100行）**：建议使用单线程或2-3个线程
- **中等数据量（100-1000行）**：建议使用3-5个线程
- **大数据量（>1000行）**：建议使用5-8个线程
- **超大数据量（>5000行）**：可尝试8-10个线程

### 性能对比
- 多线程通常可提升2-5倍处理速度
- 具体提升取决于网络状况和API响应速度
- 建议先运行`performance_test.py`测试最佳线程数

### 稳定性考虑
- 网络不稳定时建议使用较少线程数
- API有频率限制时会自动重试
- 多线程模式下仍保持完整的错误处理和日志记录

## 注意事项

- 多线程模式下API调用更频繁，请确保网络稳定
- 如果API调用失败，会记录错误日志但不会中断程序
- 建议先使用测试模式验证功能正常
- 多线程完整分析速度显著提升，但仍需耐心等待
- 建议在运行前备份原始数据文件

## 敏感内容分类标准

### 涉政言论
- 政治敏感词汇
- 政治立场表达
- 政府批评
- 政治人物评论
- 政党派别讨论

### 其他敏感内容（详细分类）
- 色情内容：色情、性暗示、低俗内容等
- 暴力内容：暴力描述、血腥内容、威胁恐吓等
- 歧视言论：种族歧视、性别歧视、地域歧视等
- 谣言传播：虚假信息、未经证实的传言等
- 诈骗信息：诈骗、欺诈、虚假广告等
- 仇恨言论：煽动仇恨、极端言论等
- 违法内容：违法犯罪相关内容
- 其他敏感：其他类型敏感内容

## 重试失败记录

如果分析过程中有记录失败，可以使用重试脚本：

```bash
python retry_failed.py
```

该脚本会：
- 读取失败记录文件
- 重新尝试分析失败的记录
- 增加重试次数和等待时间
- 保存重试成功的记录

## 故障排除

1. **API连接失败**：检查网络连接，使用测试模式验证API连接
2. **Excel文件读取失败**：确保文件名为5.xlsx且格式正确
3. **内存不足**：使用测试模式或小规模分析模式
4. **分析中断**：检查日志文件，使用retry_failed.py重试失败记录
5. **重复分析**：程序会自动去重，同ID只保留最敏感的一条记录
