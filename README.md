# 敏感内容分析工具

这个Python脚本用于分析Excel文件中的敏感内容，使用大模型API进行智能识别和分类。

## 功能特点

- 读取Excel文件的Sheet1表格
- 调用大模型API分析D列的发言内容
- 自动识别敏感信息并分类为"涉政言论"和"其他"两类
- 将结果保存到不同的工作表中
- 支持测试模式和完整分析模式

## 文件说明

- `sensitive_content_analyzer.py` - 主分析脚本
- `run_analysis.py` - 简化运行脚本（推荐使用）
- `test_api.py` - API连接测试脚本
- `requirements.txt` - 依赖包列表
- `5.xlsx` - 输入数据文件
- `敏感内容分析结果.xlsx` - 输出结果文件

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：使用简化运行脚本（推荐）

```bash
python run_analysis.py
```

然后按照提示选择分析模式：
- 测试模式：分析前50行数据
- 小规模分析：分析前500行数据
- 完整分析：分析全部数据

### 方法二：直接运行主脚本

```bash
python sensitive_content_analyzer.py
```

## 数据格式要求

确保你的Excel文件名为`5.xlsx`，包含Sheet1工作表，数据格式如下：
- A列：ID信息
- B列：群组信息
- C列：时间信息
- D列：发言内容（待分析）
- E列：其他信息（可选）

## 输出结果

脚本会生成`敏感内容分析结果.xlsx`文件，包含两个工作表：
- `涉政言论`：包含政治敏感内容
- `其他敏感内容`：包含其他类型的敏感内容

每个工作表包含以下列：
- id：原A列信息
- 敏感言论：原D列信息
- 时间：原C列信息
- 群组：原B列信息

## API配置

脚本使用以下API配置：
- API地址：https://one-api.zaoniao.vip
- 模型：gpt-5-nano-2025-08-07
- Token：已内置在脚本中

## 测试API连接

如果遇到连接问题，可以运行测试脚本：

```bash
python test_api.py
```

## 注意事项

- 脚本会在API调用之间添加0.5秒延迟以避免频率限制
- 如果API调用失败，会记录错误日志但不会中断程序
- 建议先使用测试模式验证功能正常
- 完整分析可能需要数小时，请耐心等待
- 建议在运行前备份原始数据文件

## 敏感内容分类标准

### 涉政言论
- 政治敏感词汇
- 政治立场表达
- 政府批评
- 政治人物评论
- 政党派别讨论

### 其他敏感内容
- 色情内容
- 暴力内容
- 歧视言论
- 谣言传播
- 诈骗信息

## 故障排除

1. **API连接失败**：检查网络连接，运行test_api.py测试
2. **Excel文件读取失败**：确保文件名为5.xlsx且格式正确
3. **内存不足**：使用测试模式或小规模分析模式
4. **分析中断**：检查日志文件，可以重新运行继续分析
