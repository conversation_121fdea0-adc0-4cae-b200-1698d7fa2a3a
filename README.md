# 敏感内容分析工具

这个Python脚本用于分析Excel文件中的敏感内容，使用大模型API进行智能识别和分类。

## 功能特点

- 读取Excel文件的Sheet1表格
- 调用大模型API分析D列的发言内容
- 自动识别敏感信息并分类为"涉政言论"和"其他"两类
- 将结果保存到不同的工作表中

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 确保你的Excel文件名为`5.xlsx`，包含Sheet1工作表
2. 工作表应包含以下列：
   - A列：ID信息
   - B列：群组信息
   - C列：时间信息
   - D列：发言内容（待分析）

3. 运行脚本：
```bash
python sensitive_content_analyzer.py
```

## 输出结果

脚本会生成`敏感内容分析结果.xlsx`文件，包含两个工作表：
- `涉政言论`：包含政治敏感内容
- `其他敏感内容`：包含其他类型的敏感内容

每个工作表包含以下列：
- id：原A列信息
- 敏感言论：原D列信息
- 时间：原C列信息
- 群组：原B列信息

## 注意事项

- 脚本会在API调用之间添加0.5秒延迟以避免频率限制
- 如果API调用失败，会记录错误日志但不会中断程序
- 建议在运行前备份原始数据文件
