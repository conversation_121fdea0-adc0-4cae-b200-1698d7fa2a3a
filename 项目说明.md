# 敏感内容分析工具 - 项目说明

## 项目概述

本项目是一个基于大模型API的敏感内容分析工具，专门用于分析Excel文件中的文本内容，自动识别和分类敏感信息。

## 技术架构

- **编程语言**: Python 3.x
- **主要依赖**: pandas, requests, openpyxl
- **API服务**: https://one-api.zaoniao.vip (gpt-5-nano-2025-08-07模型)
- **输入格式**: Excel文件 (.xlsx)
- **输出格式**: Excel文件 (.xlsx)

## 核心功能

### 1. 数据读取
- 自动读取Excel文件的Sheet1工作表
- 支持标准的A、B、C、D列格式
- 智能处理列名和数据格式

### 2. 内容分析
- 调用大模型API分析D列的发言内容
- 智能识别敏感信息类型
- 支持两种分类：涉政言论、其他敏感内容

### 3. 结果输出
- 生成结构化的Excel报告
- 分别保存不同类型的敏感内容
- 保留原始数据的关联信息

## 文件结构

```
项目目录/
├── sensitive_content_analyzer.py  # 主分析脚本
├── run_analysis.py               # 简化运行脚本
├── test_api.py                   # API测试脚本
├── requirements.txt              # 依赖包列表
├── 运行分析.bat                  # Windows批处理文件
├── README.md                     # 使用说明
├── 项目说明.md                   # 项目文档
├── 5.xlsx                        # 输入数据文件
└── 敏感内容分析结果.xlsx          # 输出结果文件
```

## 使用流程

### 快速开始
1. 双击运行 `运行分析.bat`
2. 按照提示选择分析模式
3. 等待分析完成
4. 查看生成的结果文件

### 命令行使用
```bash
# 安装依赖
pip install -r requirements.txt

# 运行简化脚本
python run_analysis.py

# 或直接运行主脚本
python sensitive_content_analyzer.py
```

## 分析模式

### 测试模式 (50行)
- 适用于功能验证
- 快速获得结果
- 用时约2-3分钟

### 小规模分析 (500行)
- 适用于样本分析
- 平衡速度和覆盖面
- 用时约20-30分钟

### 完整分析 (全部数据)
- 适用于正式分析
- 处理所有数据
- 用时可能数小时

## 敏感内容识别标准

### 涉政言论
- 政治敏感词汇和表达
- 对政府、政党的评论
- 政治立场和观点表达
- 政治人物相关讨论
- 政治事件和政策评论

### 其他敏感内容
- 色情和低俗内容
- 暴力和血腥内容
- 歧视和仇恨言论
- 谣言和虚假信息
- 诈骗和违法信息

## 技术特点

### 1. 智能分析
- 使用先进的大语言模型
- 上下文理解能力强
- 分类准确度高

### 2. 稳定性保障
- API调用失败自动重试
- 网络异常处理机制
- 进度日志记录

### 3. 性能优化
- 批量处理机制
- 合理的API调用频率控制
- 内存使用优化

### 4. 用户友好
- 多种运行方式
- 清晰的进度提示
- 详细的错误信息

## 输出结果说明

### 结果文件结构
生成的Excel文件包含两个工作表：

#### 涉政言论工作表
| 列名 | 说明 | 来源 |
|------|------|------|
| id | 记录标识符 | 原A列 |
| 敏感言论 | 敏感内容文本 | 原D列 |
| 时间 | 发言时间 | 原C列 |
| 群组 | 群组信息 | 原B列 |

#### 其他敏感内容工作表
结构同上，包含非政治类敏感内容。

### 统计信息
- 总分析行数
- 涉政言论数量
- 其他敏感内容数量
- 分析耗时

## 注意事项

### 1. 数据安全
- 所有数据通过HTTPS传输
- 不会永久存储分析内容
- 建议备份原始数据

### 2. 使用限制
- API调用有频率限制
- 大量数据分析需要时间
- 网络连接要求稳定

### 3. 结果准确性
- AI分析结果仅供参考
- 建议人工复核重要结果
- 不同模型可能有差异

## 故障排除

### 常见问题
1. **API连接失败**: 检查网络连接，运行test_api.py
2. **Excel读取错误**: 确保文件格式正确
3. **内存不足**: 使用小规模分析模式
4. **分析中断**: 检查日志，重新运行

### 技术支持
- 查看详细日志文件
- 运行测试脚本诊断
- 检查依赖包版本

## 更新日志

### v1.0 (当前版本)
- 基础敏感内容分析功能
- 支持Excel文件处理
- 多种分析模式
- 完整的错误处理机制

## 许可证

本项目仅供学习和研究使用。
