# 敏感内容分析工具 - 项目完成说明

## 🎯 项目概述

本项目是一个基于大模型API的敏感内容分析工具，能够智能分析Excel文件中的文本内容，自动识别和分类敏感信息。

## ✨ 核心功能

### 1. 智能文件名处理
- ✅ 支持任意Excel文件名输入
- ✅ 自动添加.xlsx扩展名
- ✅ 文件存在性验证
- ✅ 友好的用户提示界面

### 2. 详细敏感内容分类
- ✅ **涉政言论**：政治敏感词汇、政治立场表达等
- ✅ **色情内容**：色情、性暗示、低俗内容等
- ✅ **暴力内容**：暴力描述、血腥内容、威胁恐吓等
- ✅ **歧视言论**：种族、性别、地域歧视等
- ✅ **谣言传播**：虚假信息、未经证实传言等
- ✅ **诈骗信息**：诈骗、欺诈、虚假广告等
- ✅ **仇恨言论**：煽动仇恨、极端言论等
- ✅ **违法内容**：违法犯罪相关内容
- ✅ **其他敏感**：其他类型敏感内容

### 3. 智能去重机制
- ✅ 同个ID只保留最敏感的一条言论
- ✅ 敏感程度评分（1-10级）
- ✅ 涉政言论优先级最高

### 4. 强化错误处理
- ✅ 自动重试机制（失败时重试3次，递增等待时间）
- ✅ 失败记录单独存储
- ✅ 专用重试脚本处理失败记录

### 5. 完整日志系统
- ✅ analysis.log：完整分析过程日志
- ✅ error.log：专门的错误日志
- ✅ 同时输出到控制台和文件

### 6. 动态输出文件名
- ✅ 根据输入文件名生成输出文件名
- ✅ 格式：`[输入文件名]_敏感内容分析结果.xlsx`
- ✅ 失败记录：`[输入文件名]_敏感内容分析结果_失败记录.csv`

## 📁 项目文件结构

```
敏感内容分析工具/
├── sensitive_content_analyzer.py  # 核心分析引擎
├── run_analysis.py               # 用户友好的运行界面
├── retry_failed.py               # 失败记录重试工具
├── requirements.txt              # 依赖包列表
├── 运行分析.bat                  # Windows一键运行脚本
├── README.md                     # 详细使用说明
├── 使用说明.txt                  # 快速使用指南
└── 项目完成说明.md               # 本文件
```

## 🚀 使用方式

### 方式一：一键运行（推荐）
```bash
双击 运行分析.bat
```

### 方式二：命令行运行
```bash
python run_analysis.py
```

### 方式三：直接运行核心脚本
```bash
python sensitive_content_analyzer.py
```

## 📊 输出结果

### 主要结果文件
- `[文件名]_敏感内容分析结果.xlsx`

### 工作表内容
1. **涉政言论** - 政治敏感内容
2. **其他敏感内容** - 其他类型敏感内容汇总
3. **各分类工作表** - 色情、暴力、歧视等详细分类
4. **分析失败记录** - 分析失败的记录（如有）

### 日志文件
- `analysis.log` - 完整分析日志
- `error.log` - 错误日志
- `[文件名]_敏感内容分析结果_失败记录.csv` - 失败记录（如有）

## 🔧 技术特点

### 1. 编码兼容性
- ✅ 解决Windows控制台GBK编码问题
- ✅ 移除所有emoji字符，确保兼容性
- ✅ 支持中文文件名和路径

### 2. 错误处理
- ✅ 网络连接异常自动重试
- ✅ API调用失败处理
- ✅ 文件读写异常处理
- ✅ 用户输入验证

### 3. 性能优化
- ✅ 支持测试模式（50行）
- ✅ 支持小规模分析（500行）
- ✅ 支持完整分析（全部数据）
- ✅ API调用频率控制

## 📈 测试验证

### 测试结果
- ✅ 成功分析3行测试数据
- ✅ 识别出2条涉政言论（敏感度3和5）
- ✅ 去重功能正常（同ID保留敏感度更高的记录）
- ✅ 文件名动态生成正常
- ✅ 日志记录完整

### 分析能力
- ✅ 准确识别政治敏感内容
- ✅ 敏感程度评分合理
- ✅ 分类准确度高

## 🎉 项目优势

1. **用户友好** - 支持任意文件名，自动提示和验证
2. **功能完整** - 9大类敏感内容详细分类
3. **稳定可靠** - 完善的错误处理和重试机制
4. **高效智能** - 去重机制和敏感程度评分
5. **易于使用** - 多种运行方式，清晰的操作指引
6. **兼容性强** - 解决Windows编码问题，支持中文

## 💡 使用建议

1. **首次使用**：建议使用测试模式验证功能
2. **大量数据**：建议先小规模分析，确认无误后完整分析
3. **失败处理**：如有失败记录，使用retry_failed.py重试
4. **结果查看**：查看日志文件了解详细分析过程

## 🔮 项目价值

本工具为企业和组织提供了一个强大的内容审核解决方案，能够：
- 自动化处理大量文本数据
- 准确识别各类敏感内容
- 提供详细的分类和评分
- 支持灵活的文件处理
- 确保数据处理的完整性和可靠性

项目已完成所有核心功能开发和测试验证，可以立即投入生产使用。
